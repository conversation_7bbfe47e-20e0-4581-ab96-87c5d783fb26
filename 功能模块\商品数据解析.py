#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品数据解析模块
用于解析JSON格式的商品数据并下载图片
"""

import os
import json
import requests
import time
from datetime import datetime
from pathlib import Path
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QMessageBox


class ProductDataParser(QThread):
    """商品数据解析器"""
    
    # 信号定义
    progress_updated = pyqtSignal(int)  # 进度更新
    status_updated = pyqtSignal(str)    # 状态更新
    log_updated = pyqtSignal(str)       # 日志更新
    parsing_finished = pyqtSignal(bool, str)  # 解析完成
    
    def __init__(self, file_path, is_batch=False):
        super().__init__()
        self.file_path = file_path
        self.is_batch = is_batch
        self.output_dir = Path("商品文件夹")
        self.max_retries = 3
        self.retry_delay = 2
        
    def run(self):
        """运行解析任务"""
        try:
            if self.is_batch:
                self.parse_batch()
            else:
                self.parse_single()
        except Exception as e:
            self.log_updated.emit(f"解析过程中发生错误: {str(e)}")
            self.parsing_finished.emit(False, str(e))
    
    def parse_single(self):
        """解析单个JSON文件"""
        self.status_updated.emit("正在解析单个商品文件...")
        self.log_updated.emit(f"开始解析文件: {self.file_path}")
        
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            success = self.process_product_data(data, Path(self.file_path).stem)
            
            if success:
                self.log_updated.emit("单个商品解析完成")
                self.parsing_finished.emit(True, "解析完成")
            else:
                self.parsing_finished.emit(False, "解析失败")
                
        except Exception as e:
            self.log_updated.emit(f"解析单个文件失败: {str(e)}")
            self.parsing_finished.emit(False, str(e))
    
    def parse_batch(self):
        """批量解析文件夹中的JSON文件"""
        self.status_updated.emit("正在批量解析商品文件...")
        self.log_updated.emit(f"开始批量解析文件夹: {self.file_path}")
        
        try:
            folder_path = Path(self.file_path)
            json_files = list(folder_path.glob("*.json"))
            
            if not json_files:
                self.log_updated.emit("文件夹中没有找到JSON文件")
                self.parsing_finished.emit(False, "没有找到JSON文件")
                return
            
            total_files = len(json_files)
            success_count = 0
            
            for i, json_file in enumerate(json_files):
                self.log_updated.emit(f"正在处理文件 {i+1}/{total_files}: {json_file.name}")
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if self.process_product_data(data, json_file.stem):
                        success_count += 1
                    
                    # 更新进度
                    progress = int((i + 1) / total_files * 100)
                    self.progress_updated.emit(progress)
                    
                except Exception as e:
                    self.log_updated.emit(f"处理文件 {json_file.name} 失败: {str(e)}")
            
            self.log_updated.emit(f"批量解析完成，成功处理 {success_count}/{total_files} 个文件")
            self.parsing_finished.emit(True, f"批量解析完成，成功 {success_count}/{total_files}")
            
        except Exception as e:
            self.log_updated.emit(f"批量解析失败: {str(e)}")
            self.parsing_finished.emit(False, str(e))
    
    def process_product_data(self, data, filename):
        """处理单个商品数据"""
        try:
            # 创建商品文件夹
            product_folder = self.output_dir / filename
            product_folder.mkdir(parents=True, exist_ok=True)
            
            # 提取基本信息
            product_desc = data.get("商品描述", "")
            images = data.get("图片列表", [])

            # 保存商品信息为TXT格式（只保存商品描述，不包含标题）
            info_file = product_folder / "商品信息.txt"
            with open(info_file, 'w', encoding='utf-8') as f:
                # 只写入商品描述内容，保持原有换行
                if product_desc:
                    f.write(product_desc)
                else:
                    f.write("未找到商品描述内容")

            self.log_updated.emit(f"已保存商品信息到: {info_file}")
            
            # 下载图片
            if images:
                self.log_updated.emit(f"开始下载 {len(images)} 张图片...")
                success_count = self.download_images(images, product_folder)
                self.log_updated.emit(f"图片下载完成，成功下载 {success_count}/{len(images)} 张")
            else:
                self.log_updated.emit("没有找到图片信息")
            
            return True
            
        except Exception as e:
            self.log_updated.emit(f"处理商品数据失败: {str(e)}")
            return False
    
    def download_images(self, images, folder_path):
        """下载图片"""
        success_count = 0
        
        for img_info in images:
            try:
                img_url = img_info.get("图片地址", "")
                img_seq = img_info.get("序号", 1)
                
                if not img_url:
                    self.log_updated.emit(f"图片 {img_seq} 没有有效的URL")
                    continue
                
                # 确定文件扩展名
                if img_url.lower().endswith('.webp'):
                    ext = '.webp'
                elif img_url.lower().endswith('.png'):
                    ext = '.png'
                else:
                    ext = '.jpg'
                
                filename = f"图片_{img_seq:02d}{ext}"
                file_path = folder_path / filename
                
                # 下载图片（带重试机制）
                if self.download_image_with_retry(img_url, file_path):
                    success_count += 1
                    self.log_updated.emit(f"成功下载图片: {filename}")
                else:
                    self.log_updated.emit(f"下载图片失败: {filename}")
                
            except Exception as e:
                self.log_updated.emit(f"处理图片 {img_info.get('序号', '未知')} 时出错: {str(e)}")
        
        return success_count
    
    def download_image_with_retry(self, url, file_path):
        """带重试机制的图片下载"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        for attempt in range(self.max_retries):
            try:
                response = requests.get(url, headers=headers, timeout=30)
                response.raise_for_status()
                
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                return True
                
            except Exception as e:
                self.log_updated.emit(f"下载尝试 {attempt + 1}/{self.max_retries} 失败: {str(e)}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
        
        return False


class ProductParserManager:
    """商品解析管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.parser_thread = None
    
    def parse_single_file(self, file_path):
        """解析单个文件"""
        if self.parser_thread and self.parser_thread.isRunning():
            self.main_window.show_message("提示", "解析任务正在进行中，请等待完成", QMessageBox.Information)
            return
        
        self.parser_thread = ProductDataParser(file_path, is_batch=False)
        self.connect_signals()
        self.parser_thread.start()
    
    def parse_batch_folder(self, folder_path):
        """批量解析文件夹"""
        if self.parser_thread and self.parser_thread.isRunning():
            self.main_window.show_message("提示", "解析任务正在进行中，请等待完成", QMessageBox.Information)
            return
        
        self.parser_thread = ProductDataParser(folder_path, is_batch=True)
        self.connect_signals()
        self.parser_thread.start()
    
    def connect_signals(self):
        """连接信号"""
        self.parser_thread.progress_updated.connect(self.main_window.update_progress)
        self.parser_thread.status_updated.connect(self.main_window.update_status)
        self.parser_thread.log_updated.connect(self.main_window.add_log)
        self.parser_thread.parsing_finished.connect(self.main_window.on_parsing_finished)
    
    def stop_parsing(self):
        """停止解析"""
        if self.parser_thread and self.parser_thread.isRunning():
            self.parser_thread.terminate()
            self.parser_thread.wait()
